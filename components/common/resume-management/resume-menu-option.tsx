import Image from "next/image";
import { ReactNode, useState } from "react";
import { CircleAlert, EllipsisVertical } from "lucide-react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";

export type ActionOption = {
  label: string;
  image: string;
  onClick?: () => void;
};

type ActionDropdownProps = {
  options: ActionOption[];
  onOptionClick: (label: string) => void;
  triggerIcon?: ReactNode;
  className?: string;
  align?: "start" | "center" | "end";
  side?: "top" | "right" | "bottom" | "left";
  sideOffset?: number;
};

const ResumeActionDropdown = ({
  options,
  onOptionClick,
  triggerIcon = <EllipsisVertical className="flex-shrink-0" />,
  className = "w-48 p-2 rounded-xl",
  align = "end",
  side = "bottom",
  sideOffset = 4,
}: ActionDropdownProps) => {
  return (
    <Dialog>
      <DropdownMenu modal={false}>
        <DropdownMenuTrigger asChild>{triggerIcon}</DropdownMenuTrigger>
        <DropdownMenuContent
          side={side}
          align={align}
          alignOffset={-10}
          sideOffset={sideOffset}
          className={className}
        >
          <DropdownMenuGroup className="flex flex-col gap-2">
            {options.map((item, index) => (
              <DropdownMenuItem
                key={`${item.label}-${index}`}
                className="flex items-center gap-1 cursor-pointer hover:bg-[#F9F9F9]"
                onClick={() =>
                  item.onClick ? item.onClick() : onOptionClick(item.label)
                }
              >
                <Image
                  alt={item.label}
                  src={item.image}
                  width={24}
                  height={24}
                />
                <p className="text-sm text-[#06042B] whitespace-nowrap">
                  {item.label}
                </p>
              </DropdownMenuItem>
            ))}
          </DropdownMenuGroup>
        </DropdownMenuContent>
      </DropdownMenu>
    </Dialog>
  );
};

const AlertDeleteResume = ({
  openDialogDelete,
  setOpenDialogDelete,
  onOk,
}: {
  openDialogDelete: boolean;
  setOpenDialogDelete: (open: boolean) => void;
  onOk: VoidFunction;
}) => {
  return (
    <AlertDialog open={openDialogDelete} onOpenChange={setOpenDialogDelete}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle className="flex items-center gap-2 font-medium">
            <CircleAlert color="#FAAD14" />
            Are you sure delete this Resume?
          </AlertDialogTitle>
          <AlertDialogDescription className="text-left">
            This action cannot be undone
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter className="flex flex-row items-center justify-end gap-2">
          <AlertDialogCancel className="py-0 !m-0 text-black/90 focus-visible:ring-0 focus-visible:ring-offset-0 focus-visible:ring-[#D9D9D9]">
            No
          </AlertDialogCancel>
          <AlertDialogAction
            className="py-0 !m-0 text-background bg-main hover:bg-main"
            onClick={onOk}
          >
            Yes
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};

const DialogUpdateResume = ({
  name,
  openDialogUpdate,
  setOpenDialogUpdate,
  onOk,
}: {
  name: string;
  openDialogUpdate: boolean;
  setOpenDialogUpdate: (open: boolean) => void;
  onOk: (resumeName: string) => void;
}) => {
  const [resumeName, setResumeName] = useState<string>(name);

  return (
    <Dialog open={openDialogUpdate} onOpenChange={setOpenDialogUpdate}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="font-medium">Change Resume Name</DialogTitle>
        </DialogHeader>
        <Input
          value={resumeName}
          onChange={(e) => setResumeName(e.target.value)}
        />
        <DialogFooter className="justify-end gap-2">
          <DialogClose asChild>
            <Button
              variant="secondary"
              className="py-0 !m-0 focus-visible:ring-0 focus-visible:ring-offset-0 focus-visible:ring-[#D9D9D9]"
            >
              Cancel
            </Button>
          </DialogClose>
          <Button className="py-0 !m-0" onClick={() => onOk(resumeName)}>
            Ok
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export { ResumeActionDropdown, AlertDeleteResume, DialogUpdateResume };
